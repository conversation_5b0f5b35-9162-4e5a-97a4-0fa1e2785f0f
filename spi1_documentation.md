# SPI1 控制器文档

## 基本信息

- **节点名称**: `spi1: spi@0x14203000`
- **兼容性**: `lo<PERSON><PERSON>,ls-spi-v1`
- **基地址**: `0x14203000`
- **寄存器空间**: 4KB (`0x1000`)

## 主要参数

| 参数 | 值 | 说明 |
|------|----|----|
| compatible | "loongson,ls-spi-v1" | 龙芯 SPI 控制器版本1 |
| status | "disabled" | 默认禁用状态 |
| reg | `<0 0x14203000 0 0x1000>` | 寄存器地址和大小 |
| spi-max-frequency | 50000000 | 最大频率 50MHz |
| interrupt-parent | `<&icu>` | 中断父控制器 ICU |
| interrupts | 6 | 中断号 |
| #address-cells | 1 | 子设备地址单元数 |
| #size-cells | 0 | 子设备大小单元数 |

## 功能特性

- 支持标准 SPI 协议
- 最大工作频率 50MHz
- 中断驱动模式
- 支持多个片选设备

## 使用方法

启用 SPI1 并连接设备：

```dts
&spi1 {
    status = "okay";
    
    device@0 {
        compatible = "device-compatible-string";
        reg = <0>;  // 片选0
        spi-max-frequency = <25000000>;
    };
};
```
