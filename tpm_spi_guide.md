# LS2P500 开发版 TPM SPI 配置指南

## 概述

本指南专门针对 **LS2P500 开发版**，详细说明如何配置 **SPI1 控制器**与 **TPM 芯片**的通信。LS2P500 使用 BIOS 通过 SPI1 接口与 TPM 芯片进行安全通信。

## 硬件架构

### LS2P500 SPI 控制器配置
- **SPI0**: 基址 `0x14010000` - 通用 SPI 设备（已被其他设备占用）
- **SPI1**: 基址 `0x14203000` - **TPM 专用控制器**（推荐）
- **SPI2**: 基址 `0x15109000` - 备用控制器
- **SPI3**: 基址 `0x15203000` - 备用控制器

### TPM 硬件连接（SPI1）
TPM 芯片通过 5 根信号线连接到 LS2P500 的 SPI1 控制器：
- **CLK** - 时钟信号（GPIO A0_12）
- **MOSI** - 主控发送数据（GPIO A0_13）
- **MISO** - 主控接收数据（GPIO A0_14）
- **CS** - 片选信号（GPIO A0_15）
- **IRQ** - 中断信号（连接到 ICU 中断控制器）

## 快速配置（推荐）

### 设备树配置文件
**文件路径**：`arch/loongarch/boot/dts/loongson/loongson64_2p500.dts`

```dts
&spi1 {                                    // 使用 SPI1 控制器（TPM 专用）
    status = "okay";                       // 启用 SPI1 控制器
    pinctrl-names = "default";             // 引脚控制配置
    pinctrl-0 = <&spi1_pin>;              // 使用 SPI1 引脚配置

    tpm@0 {                               // TPM 设备节点
        compatible = "tcg,tpm_tis-spi";   // 通用 TPM SPI 驱动
        reg = <0>;                        // 片选 0（CS0）
        spi-max-frequency = <10000000>;   // 10MHz（TPM 标准频率）
        interrupt-parent = <&icu>;        // ICU 中断控制器
        interrupts = <6 IRQ_TYPE_LEVEL_LOW>; // 中断 6，低电平触发
    };
};
```

### 配置参数说明

**`&spi1`** - SPI1 控制器选择
- LS2P500 专门为 TPM 预留的 SPI 控制器
- 基址：`0x14203000`，支持中断（中断号 6）
- 引脚：GPIO A0_12-15（CLK, MOSI, MISO, CS）

**`tpm@0`** - TPM 设备节点
- `tpm` = 设备类型标识
- `@0` = 片选地址，对应 `reg = <0>`

**`compatible = "tcg,tpm_tis-spi"`** - 驱动匹配
- 通用 TPM TIS SPI 驱动，兼容大多数 TPM 2.0 芯片
- 其他选项：`"infineon,slb9670"`, `"st,st33htpm-spi"`

**`spi-max-frequency = <10000000>`** - 通信频率
- 10MHz 是 TPM 芯片的标准工作频率
- 过高频率可能导致通信不稳定

**`interrupt-parent = <&icu>`** - 中断控制器
- LS2P500 使用 ICU（Interrupt Control Unit）管理中断
- 中断号 6 专门分配给 SPI1 控制器

## 内核配置

### 必需的内核选项
```bash
# 在 .config 文件中确保以下选项已启用
CONFIG_TCG_TIS_SPI=y              # TPM SPI 驱动
CONFIG_SPI=y                      # SPI 子系统
CONFIG_SPI_MASTER=y               # SPI 主控制器支持
CONFIG_SPI_LS_V1=y                # LoongArch SPI v1 驱动
```

### 编译验证
```bash
# 检查 TPM SPI 相关配置
grep -E "CONFIG_TCG_TIS_SPI|CONFIG_SPI_LS_V1" .config

# 编译设备树
make ARCH=loongarch CROSS_COMPILE=loongarch64-linux-gnu- dtbs
```

## 工作原理

### TPM SPI 通信流程
1. **驱动加载**：`tpm_tis_spi.c` 驱动注册到 SPI 总线
2. **设备匹配**：根据设备树中的 `compatible` 属性匹配驱动
3. **设备初始化**：调用 `tpm_tis_spi_probe()` 初始化 TPM 设备
4. **SPI 通信**：实现 TPM 专用的 SPI 传输协议
5. **设备注册**：创建 `/dev/tpm0` 字符设备供应用使用

### LS2P500 SPI1 驱动架构
```
应用层 (/dev/tpm0)
    ↓
TPM 核心 (tpm-chip.c)
    ↓
TIS 协议层 (tpm_tis_core.c)
    ↓
SPI 物理层 (tpm_tis_spi.c)
    ↓
LS SPI v1 驱动 (spi-ls-v1.c)
    ↓
SPI1 硬件控制器 (0x14203000)
```

### TPM SPI 通信协议
TPM 使用特殊的 4 字节头部格式：
```
[控制字节][0xD4][地址高][地址低]
```
- **控制字节**：`0x80`(读) 或 `0x00`(写) + 数据长度-1
- **0xD4**：TPM SPI 协议固定字节
- **地址**：TPM 寄存器地址（16位）

### 等待机制
TPM 可能需要时间处理，驱动实现轮询等待：
1. 发送命令头部，检查响应的最低位
2. 如果最低位为 0，表示 TPM 忙碌
3. 发送空字节轮询，直到最低位为 1
4. 继续数据传输

## BIOS 集成说明

### BIOS 与 TPM 通信
- LS2P500 BIOS 在启动时通过 SPI1 初始化 TPM 芯片
- BIOS 负责 TPM 的基础配置和安全启动验证
- Linux 内核接管后继续使用相同的 SPI1 接口

### 共享访问机制
- BIOS 阶段：独占访问 TPM
- 内核启动：接管 SPI1 控制器
- 运行时：内核驱动管理所有 TPM 操作

## 详细配置步骤

### 步骤 1：修改设备树配置

**编辑文件**：`arch/loongarch/boot/dts/loongson/loongson64_2p500.dts`

```bash
# 在现有的 SPI0 配置后添加 SPI1 配置
vim arch/loongarch/boot/dts/loongson/loongson64_2p500.dts
```

**添加 SPI1 TPM 配置**：
```dts
&spi1 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi1_pin>;

    tpm@0 {
        compatible = "tcg,tpm_tis-spi";
        reg = <0>;
        spi-max-frequency = <10000000>;
        interrupt-parent = <&icu>;
        interrupts = <6 IRQ_TYPE_LEVEL_LOW>;
    };
};
```

### 步骤 2：验证引脚配置

**检查引脚配置文件**：`arch/loongarch/boot/dts/loongson/2p500-pinctrl.dtsi`

SPI1 引脚已预定义：
```dts
spi1_pin: spi1-pin{
    loongson,pinmux = <&gpa0 12 15>;        // GPIO A0_12-15
    loongson,pinmux-funcsel = <PINCTL_FUNCTIONMAIN>;
};
```

### 步骤 3：编译和验证

**编译设备树**：
```bash
make ARCH=loongarch CROSS_COMPILE=loongarch64-linux-gnu- dtbs

# 验证编译结果
ls arch/loongarch/boot/dts/loongson/loongson64_2p500.dtb
```

**检查配置**：
```bash
# 反编译查看 TPM 配置
dtc -I dtb -O dts arch/loongarch/boot/dts/loongson/loongson64_2p500.dtb | grep -A10 -B5 tpm
```

### 步骤 4：内核配置验证

**确认必需选项**：
```bash
# 检查 TPM 和 SPI 相关配置
grep -E "CONFIG_TCG_TIS_SPI|CONFIG_SPI_LS_V1|CONFIG_SPI" .config

# 应该看到：
# CONFIG_TCG_TIS_SPI=y
# CONFIG_SPI_LS_V1=y
# CONFIG_SPI=y
# CONFIG_SPI_MASTER=y
```

## 运行时验证

### 在 LS2P500 开发板上验证

**重要**：以下命令需要在 LS2P500 开发板上执行，不是在编译主机上。

#### 基本验证

**检查 SPI1 设备注册**：
```bash
# 检查 SPI1 设备是否识别
ls /sys/bus/spi/devices/
# 预期输出：spi1.0（表示 SPI1 上的设备 0）

# 检查 SPI1 控制器状态
cat /sys/class/spi_master/spi1/device/uevent
```

**检查 TPM 设备**：
```bash
# 检查 TPM 字符设备
ls /dev/tpm*
# 预期输出：/dev/tpm0 /dev/tpmrm0

# 检查 TPM 设备属性
ls /sys/class/tpm/tpm0/
# 应该看到：device_id, enabled, owned, temp_deactivated 等文件
```

#### 内核日志验证

**查看 TPM 初始化日志**：
```bash
# 查看 TPM 相关的内核消息
dmesg | grep -i tpm
# 预期看到类似：
# tpm_tis_spi spi1.0: TPM 2.0 (device-id 0x1A, rev-id 78)

# 查看 SPI1 控制器日志
dmesg | grep -i "spi1\|ls-spi-v1"
```

#### 中断验证

**检查中断配置**：
```bash
# 查看中断统计
cat /proc/interrupts | grep -E "6.*spi1"
# 应该看到 SPI1 控制器的中断统计

# 查看 ICU 中断控制器
cat /proc/interrupts | grep icu
```

### 常见问题排查

#### 问题 1：SPI1 设备未识别
**现象**：`/sys/bus/spi/devices/` 下没有 `spi1.0`

**排查步骤**：
```bash
# 1. 检查 SPI1 控制器是否启用
cat /sys/class/spi_master/spi1/device/uevent 2>/dev/null || echo "SPI1 未启用"

# 2. 检查设备树配置
dmesg | grep "spi1.*okay"

# 3. 检查驱动加载
lsmod | grep spi_ls_v1
```

**解决方法**：
- 确认设备树中 `&spi1` 的 `status = "okay"`
- 检查 `CONFIG_SPI_LS_V1=y` 配置
- 重新编译设备树和内核

#### 问题 2：TPM 通信超时
**现象**：`dmesg` 显示 TPM 操作超时

**排查步骤**：
```bash
# 检查 SPI 频率设置
cat /sys/bus/spi/devices/spi1.0/max_speed_hz
# 应该显示 10000000 (10MHz)

# 检查 TPM 设备状态
cat /sys/class/tpm/tpm0/device_id 2>/dev/null || echo "TPM 未就绪"
```

**解决方法**：
- 降低 `spi-max-frequency` 到 5MHz
- 检查硬件连接和信号质量
- 确认 TPM 芯片型号兼容性

#### 问题 3：中断不工作
**现象**：TPM 工作但响应较慢

**排查步骤**：
```bash
# 检查中断计数是否增加
grep "6.*spi1" /proc/interrupts
# 在 TPM 操作前后对比中断计数
```

**解决方法**：
- 检查 ICU 中断控制器配置
- 确认中断号 6 未被其他设备占用
- 可以禁用中断，使用轮询模式

### 调试工具

#### 启用详细日志
```bash
# 启用 TPM SPI 驱动调试
echo 'module tpm_tis_spi +p' > /sys/kernel/debug/dynamic_debug/control

# 启用 LS SPI v1 驱动调试
echo 'module spi_ls_v1 +p' > /sys/kernel/debug/dynamic_debug/control

# 查看详细日志
dmesg -w | grep -E "(tpm|spi1)"
```

## 性能优化建议

### SPI 频率调优
```dts
# 保守设置（推荐）
spi-max-frequency = <10000000>;    // 10MHz，兼容性好

# 高性能设置（需测试）
spi-max-frequency = <20000000>;    // 20MHz，部分 TPM 支持

# 调试设置
spi-max-frequency = <5000000>;     // 5MHz，排查通信问题时使用
```

### 中断 vs 轮询模式
- **中断模式**：响应快，CPU 占用低，推荐用于生产环境
- **轮询模式**：兼容性好，调试简单，可作为备选方案

## 总结

### LS2P500 TPM SPI 配置要点

1. **使用 SPI1 控制器**：专门为 TPM 预留，基址 `0x14203000`
2. **引脚配置**：GPIO A0_12-15，通过 `spi1_pin` 配置
3. **中断配置**：使用 ICU 中断控制器，中断号 6
4. **频率设置**：10MHz 标准频率，确保通信稳定
5. **BIOS 兼容**：与 BIOS 共享 SPI1 接口，无冲突

### 关键配置文件
- **设备树**：`arch/loongarch/boot/dts/loongson/loongson64_2p500.dts`
- **引脚配置**：`arch/loongarch/boot/dts/loongson/2p500-pinctrl.dtsi`
- **内核配置**：`CONFIG_TCG_TIS_SPI=y`, `CONFIG_SPI_LS_V1=y`

### 验证成功标志
- SPI 设备：`/sys/bus/spi/devices/spi1.0` 存在
- TPM 设备：`/dev/tpm0` 和 `/dev/tpmrm0` 存在
- 内核日志：显示 TPM 设备 ID 和版本信息

## 附录：技术参考

### LS2P500 SPI 控制器规格

| 控制器 | 基址 | 中断 | 引脚 | 用途 |
|--------|------|------|------|------|
| SPI0 | `0x14010000` | - | A0_8-11 | 通用设备 |
| **SPI1** | `0x14203000` | 6 | A0_12-15 | **TPM 专用** |
| SPI2 | `0x15109000` | - | - | 备用 |
| SPI3 | `0x15203000` | - | - | 备用 |

### 相关驱动文件

- **TPM SPI 驱动**：`drivers/char/tpm/tpm_tis_spi.c`
- **LS SPI v1 驱动**：`drivers/spi/spi-ls-v1.c`
- **设备树配置**：`arch/loongarch/boot/dts/loongson/`

### 参考文档

- TPM 2.0 规范：TCG TPM 2.0 Library Specification
- SPI 总线规范：SPI Block Guide v03.06
- LoongArch 架构手册：LoongArch Reference Manual
